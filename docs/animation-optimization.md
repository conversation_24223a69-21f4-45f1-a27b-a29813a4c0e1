# HomePage 视图模式切换动画优化

## 问题分析

原始实现中的性能问题：
1. **过度使用 layout 动画**：多个嵌套组件同时使用 `layout` 属性导致浏览器频繁重排
2. **复杂的动画层级**：嵌套的 motion 组件增加了动画计算复杂度
3. **CSS Grid 重新计算**：`grid-template-columns` 的变化触发整个网格重新布局

## 优化策略

### 1. 使用 AnimatePresence + Key 策略
```tsx
<AnimatePresence mode="wait">
  <motion.div
    key={videoListViewMode}  // 关键：不同模式使用不同 key
    variants={gridVariants}
    initial="initial"
    animate="animate"
    exit="exit"
  >
```

**优势**：
- 避免 layout 动画，改用 transform 动画
- 每次模式切换都是新的组件实例，避免复杂的布局计算
- 更好的性能和更流畅的动画

### 2. 简化动画配置
```tsx
const viewModeTransition = {
  duration: 0.2,        // 缩短动画时长
  ease: "easeOut"       // 使用简单的缓动函数
}

const gridVariants = {
  initial: { opacity: 0, scale: 0.95, y: 10 },
  animate: { opacity: 1, scale: 1, y: 0 },
  exit: { opacity: 0, scale: 1.05, y: -10 }
}
```

### 3. GPU 加速优化
```css
transform: translateZ(0);  /* 强制 GPU 加速 */
will-change: transform;    /* 提示浏览器优化 */
```

### 4. CSS 过渡替代复杂动画
对于简单的样式变化，使用 CSS transition 而不是 Framer Motion：
```css
transition: all 0.2s ease-out;
transition: flex-direction 0.2s ease-out;
```

## 性能提升

1. **动画流畅度**：从卡顿变为丝滑
2. **响应速度**：动画时长从 250ms 优化到 200ms
3. **CPU 使用率**：减少布局计算，降低 CPU 负载
4. **内存使用**：避免复杂的动画状态管理

## 最佳实践

1. **避免 layout 动画**：尽量使用 transform 和 opacity
2. **合理使用 AnimatePresence**：对于布局变化较大的场景
3. **GPU 加速**：添加 `transform: translateZ(0)` 和 `will-change`
4. **动画时长**：保持在 150-300ms 之间
5. **缓动函数**：使用简单的缓动函数如 `easeOut`

## 注意事项

- 在动画进行时避免触发其他复杂操作
- 考虑用户的 `prefers-reduced-motion` 设置
- 在大量数据时进一步优化动画性能
