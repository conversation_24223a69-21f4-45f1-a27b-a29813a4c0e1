import { Navbar, NavbarCenter, NavbarLeft, NavbarRight } from '@renderer/components/app/Navbar'
import { useTheme } from '@renderer/contexts'
import { useSearchStore } from '@renderer/state'
import { Tooltip } from 'antd'
import { FilePlus, Grid, List, Search } from 'lucide-react'
import { FC } from 'react'
import { useTranslation } from 'react-i18next'
import styled from 'styled-components'

interface Props {
  videoListViewMode: 'grid' | 'list'
  setVideoListViewMode: (mode: 'grid' | 'list') => void
}

const HeaderNavbar: FC<Props> = ({ videoListViewMode, setVideoListViewMode }) => {
  const { t } = useTranslation()
  const { theme } = useTheme()
  const { showSearch } = useSearchStore()

  return (
    <Navbar className="home-navbar">
      <NavbarLeft>
        <Tooltip title={t('home.add_video')} mouseEnterDelay={0.8}>
          <NavbarIcon style={{ marginLeft: 10 }}>
            <FilePlus size={18} />
          </NavbarIcon>
        </Tooltip>
      </NavbarLeft>
      <NavbarCenter />
      <NavbarRight style={{ gap: 10 }}>
        <ViewModeToggleContainer>
          <ToggleButton
            theme={theme}
            $active={videoListViewMode === 'grid'}
            $position="left"
            onClick={() => setVideoListViewMode('grid')}
            className={videoListViewMode === 'grid' ? 'active' : ''}
          >
            <Grid size={18} />
          </ToggleButton>
          <ToggleButton
            theme={theme}
            $active={videoListViewMode === 'list'}
            $position="right"
            onClick={() => setVideoListViewMode('list')}
            className={videoListViewMode === 'list' ? 'active' : ''}
          >
            <List size={18} />
          </ToggleButton>
        </ViewModeToggleContainer>
        <Tooltip title={t('common.search')} mouseEnterDelay={0.8}>
          <NavbarIcon onClick={showSearch}>
            <Search size={18} />
          </NavbarIcon>
        </Tooltip>
      </NavbarRight>
    </Navbar>
  )
}

export const NavbarIcon = styled.div`
  -webkit-app-region: none;
  border-radius: 8px;
  height: 30px;
  padding: 0 7px;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  transition: all 0.2s ease-in-out;
  cursor: pointer;
  .iconfont {
    font-size: 18px;
    color: var(--color-icon);
    &.icon-a-addchat {
      font-size: 20px;
    }
    &.icon-a-darkmode {
      font-size: 20px;
    }
    &.icon-appstore {
      font-size: 20px;
    }
  }
  .anticon {
    color: var(--color-icon);
    font-size: 16px;
  }
  &:hover {
    background-color: var(--color-background);
    color: var(--color-icon-white);
  }
`

const ViewModeToggleContainer = styled.div`
  -webkit-app-region: none;
  display: flex;
  flex-direction: row;
  border-radius: 8px;
  height: 30px;
  background-color: var(--color-background-soft);
  border: 1px solid var(--color-border-soft);
  overflow: hidden;
  transition: all 0.2s ease-in-out;

  &:hover {
    background-color: var(--color-background-mute);
  }
`

const ToggleButton = styled.button<{
  theme: string
  $active: boolean
  $position: 'left' | 'right'
}>`
  -webkit-app-region: none;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 30px;
  height: 30px;
  border: none;
  border-radius: ${(props) => (props.$position === 'left' ? '8px 0 0 8px' : '0 8px 8px 0')};
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  position: relative;
  background-color: ${({ theme }) =>
    theme === 'dark' ? 'var(--color-black-soft)' : 'var(--color-white-mute)'};

  /* 添加分隔线 */
  ${(props) =>
    props.$position === 'left' &&
    `
    &::after {
      content: '';
      position: absolute;
      right: 0;
      top: 6px;
      bottom: 6px;
      width: 1px;
      background-color: var(--color-border);
      opacity: ${props.$active ? 0 : 1};
      transition: opacity 0.2s ease-in-out;
    }
  `}

  &:hover {
    background-color: ${({ theme }) =>
      theme === 'dark' ? 'var(--color-black)' : 'var(--color-white)'};
    .icon {
      color: var(--color-icon-white);
    }
  }

  &:active {
    transform: translateY(0.5px);
    background-color: ${({ theme }) =>
      theme === 'dark' ? 'var(--color-black)' : 'var(--color-white)'};
    .icon {
      color: var(--color-primary);
    }
  }

  /* active 类名样式 */
  &.active {
    background-color: ${({ theme }) =>
      theme === 'dark' ? 'var(--color-black)' : 'var(--color-white)'};

    svg {
      color: var(--color-primary);
    }
  }

  /* 图标样式 */
  svg {
    transition: all 0.2s ease-in-out;
    color: ${(props) => (props.$active ? 'var(--color-primary)' : 'inherit')};
  }
`

export default HeaderNavbar
