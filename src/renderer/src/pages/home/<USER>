import { useSettingsStore } from '@renderer/state/stores/settings.store'
import { AnimatePresence, motion } from 'framer-motion'
import React, { useState } from 'react'
import styled from 'styled-components'

import HeaderNavbar from './HeaderNavbar'

// Mock video data
const mockVideos = [
  {
    id: '1',
    title: 'React 18 新特性深度解析',
    thumbnail: 'https://i.ytimg.com/vi/bpVRWrrfM1M/maxresdefault.jpg',
    duration: 932, // 总时长（秒）
    durationText: '15:32',
    watchProgress: 0.65, // 观看进度 0-1
    createdAt: new Date('2024-01-15'),
    author: 'Tech Expert',
    views: '12.5K',
    publishedAt: '3天前',
    description: '详细介绍React 18中的并发特性、Suspense改进以及新的Hooks'
  },
  {
    id: '2',
    title: 'TypeScript 5.0 全面升级指南',
    thumbnail: 'https://i.ytimg.com/vi/EO_bNchHNPM/maxresdefault.jpg',
    duration: 1335,
    durationText: '22:15',
    watchProgress: 0.3,
    createdAt: new Date('2024-01-10'),
    author: 'Code Master',
    views: '8.9K',
    publishedAt: '1周前',
    description: 'TypeScript 5.0 新特性详解，包括装饰器、模板字符串类型等'
  },
  {
    id: '3',
    title: 'Electron 应用开发最佳实践',
    thumbnail: 'https://i.ytimg.com/vi/GrH9BaVtCgs/maxresdefault.jpg',
    duration: 1725,
    durationText: '28:45',
    watchProgress: 0.8,
    createdAt: new Date('2024-01-12'),
    author: 'Desktop Dev',
    views: '15.2K',
    publishedAt: '5天前',
    description: '从零开始构建现代桌面应用，包括性能优化和安全策略'
  },
  {
    id: '4',
    title: 'Vite + React 项目配置完全指南',
    thumbnail: 'https://i.ytimg.com/vi/89NJdbYTgJ8/maxresdefault.jpg',
    duration: 1148,
    durationText: '19:08',
    watchProgress: 0.45,
    createdAt: new Date('2024-01-16'),
    author: 'Build Tool Pro',
    views: '9.7K',
    publishedAt: '2天前',
    description: '详解 Vite 配置、插件使用和构建优化技巧'
  },
  {
    id: '5',
    title: 'CSS-in-JS vs 传统CSS方案对比',
    thumbnail: 'https://i.ytimg.com/vi/oH_sBW0bFGw/maxresdefault.jpg',
    duration: 1517,
    durationText: '25:17',
    watchProgress: 1.0,
    createdAt: new Date('2024-01-08'),
    author: 'Style Guru',
    views: '11.3K',
    publishedAt: '1周前',
    description: '深入分析 styled-components、emotion 等方案的优缺点'
  },
  {
    id: '6',
    title: '现代JavaScript异步编程模式',
    thumbnail: 'https://i.ytimg.com/vi/vn3tm0quoqE/maxresdefault.jpg',
    duration: 2022,
    durationText: '33:42',
    watchProgress: 0.15,
    createdAt: new Date('2024-01-13'),
    author: 'Async Expert',
    views: '18.6K',
    publishedAt: '4天前',
    description: 'Promise、async/await、Generator等异步模式的实战应用'
  }
]

// Helper function to format date
const formatDate = (date: Date): string => {
  return new Intl.DateTimeFormat('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  }).format(date)
}

// 优化的卡片动画配置 - 减少复杂度
const cardVariants = {
  hidden: {
    y: 15,
    opacity: 0,
    scale: 0.98
  },
  visible: {
    y: 0,
    opacity: 1,
    scale: 1,
    transition: {
      duration: 0.3,
      ease: 'easeOut' as const,
      delay: 0.02
    }
  }
}

// 优化的过渡动画配置 - 避免 layout 动画，使用 transform
const viewModeTransition = {
  duration: 0.2,
  ease: 'easeOut' as const
}

// 视图模式切换动画变体
const gridVariants = {
  initial: { opacity: 0, scale: 0.95, y: 10 },
  animate: {
    opacity: 1,
    scale: 1,
    y: 0,
    transition: viewModeTransition
  },
  exit: {
    opacity: 0,
    scale: 1.05,
    y: -10,
    transition: { ...viewModeTransition, duration: 0.15 }
  }
}

export function HomePage(): React.JSX.Element {
  const [hoveredCard, setHoveredCard] = useState<string | null>(null)
  const { videoListViewMode, setVideoListViewMode } = useSettingsStore()

  return (
    <Container>
      <HeaderNavbar
        videoListViewMode={videoListViewMode}
        setVideoListViewMode={setVideoListViewMode}
      />
      <ContentContainer id="content-container">
        <ContentBody>
          <AnimatePresence mode="wait">
            <motion.div
              key={videoListViewMode}
              variants={gridVariants}
              initial="initial"
              animate="animate"
              exit="exit"
            >
              <VideoGrid viewMode={videoListViewMode}>
                {mockVideos.map((video, index) => (
                  <MotionVideoCard
                    key={video.id}
                    variants={cardVariants}
                    initial="hidden"
                    animate="visible"
                    custom={index}
                    viewMode={videoListViewMode}
                    onHoverStart={() => setHoveredCard(video.id)}
                    onHoverEnd={() => setHoveredCard(null)}
                  >
                    <AnimatePresence>
                      <MotionCardContent
                        initial="rest"
                        animate={hoveredCard === video.id ? 'hover' : 'rest'}
                        viewMode={videoListViewMode}
                      >
                        <ThumbnailContainer viewMode={videoListViewMode}>
                          <Thumbnail src={video.thumbnail} alt={video.title} />
                          {hoveredCard === video.id && (
                            <motion.div
                              initial={{ opacity: 0 }}
                              animate={{ opacity: 1 }}
                              exit={{ opacity: 0 }}
                              transition={{ duration: 0.2 }}
                            >
                              <ThumbnailOverlay>
                                <Duration>{video.durationText}</Duration>
                                <WatchedIndicator watched={video.watchProgress >= 1}>
                                  {video.watchProgress >= 1 && (
                                    <motion.svg
                                      width="16"
                                      height="16"
                                      viewBox="0 0 16 16"
                                      fill="none"
                                      initial={{ scale: 0, rotate: -180 }}
                                      animate={{ scale: 1, rotate: 0 }}
                                      transition={{
                                        type: 'spring',
                                        stiffness: 200,
                                        damping: 15
                                      }}
                                    >
                                      <circle cx="8" cy="8" r="8" fill="#34D399" />
                                      <motion.path
                                        d="m4 8 2 2 6-4"
                                        stroke="white"
                                        strokeWidth="1.5"
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                        initial={{ pathLength: 0 }}
                                        animate={{ pathLength: 1 }}
                                        transition={{
                                          duration: 0.5,
                                          delay: 0.2,
                                          ease: 'easeOut' as const
                                        }}
                                      />
                                    </motion.svg>
                                  )}
                                </WatchedIndicator>
                              </ThumbnailOverlay>
                            </motion.div>
                          )}
                          <ProgressBarContainer>
                            <MotionProgressBar
                              progress={video.watchProgress}
                              initial={{ width: 0 }}
                              animate={{ width: `${video.watchProgress * 100}%` }}
                              transition={{
                                duration: 1.2,
                                delay: index * 0.1 + 0.5,
                                ease: [0.25, 0.46, 0.45, 0.94]
                              }}
                            />
                          </ProgressBarContainer>
                        </ThumbnailContainer>

                        <VideoInfo viewMode={videoListViewMode}>
                          <motion.div
                            initial={{ opacity: 0, y: 10 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{
                              duration: 0.5,
                              delay: index * 0.1 + 0.3,
                              ease: [0.25, 0.46, 0.45, 0.94]
                            }}
                          >
                            <VideoTitle title={video.title}>{video.title}</VideoTitle>
                            <VideoMeta>
                              <MetaRow>
                                <MetaText>{formatDate(video.createdAt)}</MetaText>
                                <MetaText>{video.publishedAt}</MetaText>
                              </MetaRow>
                            </VideoMeta>
                          </motion.div>
                        </VideoInfo>
                      </MotionCardContent>
                    </AnimatePresence>
                  </MotionVideoCard>
                ))}
              </VideoGrid>
            </motion.div>
          </AnimatePresence>
        </ContentBody>
      </ContentContainer>
    </Container>
  )
}

const Container = styled.div`
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100%;
`

const ContentContainer = styled.div`
  display: flex;
  flex: 1;
  flex-direction: row;
  height: 100vh;
`

const ContentBody = styled.div`
  flex: 1;
  padding: 24px 32px;
  overflow-y: auto;
  height: 100%;
  min-height: 0;
`

const VideoGrid = styled.div<{ viewMode: 'grid' | 'list' }>`
  display: grid;
  grid-template-columns: ${(props) => (props.viewMode === 'list' ? '1fr' : 'repeat(3, 1fr)')};
  gap: ${(props) => (props.viewMode === 'list' ? '16px' : '24px')};
  will-change: transform;
  transform: translateZ(0); /* 强制 GPU 加速 */

  @media (max-width: 900px) {
    grid-template-columns: ${(props) => (props.viewMode === 'list' ? '1fr' : 'repeat(2, 1fr)')};
  }

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 16px;
  }
`

const MotionVideoCard = styled(motion.div)<{ viewMode: 'grid' | 'list' }>`
  background: var(--color-background);
  border: 1px solid var(--color-border);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border-radius: ${(props) => (props.viewMode === 'list' ? '12px' : '20px')};
  overflow: hidden;
  cursor: pointer;
  position: relative;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  will-change: transform;
  transform: translateZ(0); /* 强制 GPU 加速 */
  transition: all 0.2s ease-out;

  &:hover {
    box-shadow: 0 20px 64px rgba(0, 0, 0, 0.15);
    border-color: var(--color-border-soft);
    background: var(--color-background-soft);
    transform: ${(props) =>
      props.viewMode === 'list'
        ? 'translateX(4px) translateZ(0)'
        : 'translateY(-2px) translateZ(0)'};
  }

  [theme-mode='dark'] & {
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);

    &:hover {
      box-shadow: 0 20px 64px rgba(0, 0, 0, 0.5);
    }
  }
`

const MotionCardContent = styled(motion.div)<{ viewMode: 'grid' | 'list' }>`
  display: flex;
  flex-direction: ${(props) => (props.viewMode === 'list' ? 'row' : 'column')};
  height: 100%;
  width: 100%;
  will-change: transform;
  transform: translateZ(0); /* 强制 GPU 加速 */
  transition: flex-direction 0.2s ease-out;
`

const Thumbnail = styled.img`
  width: 100%;
  height: 100%;
  object-fit: cover;
`

const ThumbnailOverlay = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 12px;
  background: linear-gradient(
    180deg,
    rgba(0, 0, 0, 0.3) 0%,
    transparent 30%,
    transparent 70%,
    rgba(0, 0, 0, 0.4) 100%
  );
`

const ThumbnailContainer = styled.div<{ viewMode: 'grid' | 'list' }>`
  position: relative;
  width: ${(props) => (props.viewMode === 'list' ? '240px' : '100%')};
  aspect-ratio: 16/9;
  overflow: hidden;
  border-radius: ${(props) => (props.viewMode === 'list' ? '11px 0 0 11px' : '19px 19px 0 0')};
  background: var(--color-background-mute);
  flex-shrink: 0;
  will-change: transform;
  transition: all 0.2s ease-out;
`

const Duration = styled.div`
  background: rgba(0, 0, 0, 0.75);
  backdrop-filter: blur(10px);
  color: white;
  padding: 6px 10px;
  border-radius: 8px;
  font-size: 12px;
  font-weight: 600;
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', sans-serif;
  letter-spacing: -0.2px;
  align-self: flex-end;
  margin-top: auto;
  will-change: transform;
`

const WatchedIndicator = styled.div<{ watched: boolean }>`
  display: ${(props) => (props.watched ? 'flex' : 'none')};
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-radius: 50%;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  will-change: transform;
`

const ProgressBarContainer = styled.div`
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10px);
`

const MotionProgressBar = styled(motion.div)<{ progress: number }>`
  height: 100%;
  background: linear-gradient(90deg, #007aff 0%, #5ac8fa 100%);
  position: relative;

  &::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    width: 2px;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 1px;
  }
`

const VideoInfo = styled.div<{ viewMode: 'grid' | 'list' }>`
  padding: ${(props) => (props.viewMode === 'list' ? '16px 20px' : '20px')};
  background-color: var(--color-background);
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  will-change: transform;
  transition: all 0.2s ease-out;
`

const VideoTitle = styled.h3`
  font-size: 17px;
  font-weight: 590;
  color: var(--color-text);
  margin: 0 0 12px 0;
  line-height: 1.35;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', sans-serif;
  letter-spacing: -0.3px;
  cursor: help;

  &:hover {
    color: var(--color-primary);
  }
`

const VideoMeta = styled.div`
  display: flex;
  flex-direction: column;
  gap: 8px;
`

const MetaRow = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
`

const MetaText = styled.div`
  font-size: 13px;
  font-weight: 400;
  color: #8e8e93;
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', sans-serif;
  letter-spacing: -0.1px;
`

export default HomePage
